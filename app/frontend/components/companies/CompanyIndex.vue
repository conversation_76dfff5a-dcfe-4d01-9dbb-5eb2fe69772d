<!-- ABOUTME: Company index component with inline editing and subscription cards -->
<!-- ABOUTME: Displays companies with badge selector, three-column layout, and proper authorization -->
<template>
  <div class="p-4 md:p-6">
    <!-- Success Message for Company Connection -->
    <div v-if="showSuccessMessage" class="success-message mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
      <div class="flex items-center">
        <div class="p-1 rounded-full bg-green-100 mr-3">
          <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div>
          <h3 class="text-green-800 font-semibold">{{ $t('companies.invitation_accepted', 'Pozvání akceptováno') }}</h3>
          <p class="text-green-700">{{ $t('companies.successfully_connected_to', 'Úspěšně jste se připojili k pracovnímu prostoru') }} <strong>{{ connectedCompanyName }}</strong></p>
        </div>
      </div>
    </div>
    
    <div class="flex justify-between items-center mb-4">
      <div>
        <h2 class="text-xl md:text-2xl font-semibold text-gray-800">{{ $t('companies.workspaces_title', 'Pracovní prostory') }}</h2>
        <p class="text-sm text-gray-500">{{ $t('companies.workspaces_subtitle', 'Firmy a organizace') }}</p>
      </div>
      <button @click="openCreateModal" class="btn btn-primary">{{ $t('companies.new_workspace_button', 'Nový pracovní prostor') }}</button>
    </div>
    
    <!-- Company Selector with Badge Buttons -->
    <div v-if="companyUserRoles.length > 1" class="mb-6">
      <label class="text-sm text-gray-600 block mb-2">{{ $t('companies.select_workspace', 'Vyberte pracovní prostor') }}:</label>
      <div class="flex flex-wrap gap-2">
        <button 
          v-for="role in companyUserRoles" 
          :key="role.company.id"
          @click="selectCompany(role.company.id)"
          class="company-selector-badge"
          :class="{
            'company-selector-active': currentViewCompany === role.company.id,
            'company-selector-current': role.company.id === currentTenant
          }"
        >
          {{ role.company.name }}
          <span v-if="role.company.id === currentTenant" class="ml-2 text-xs opacity-75">
            ({{ $t('companies.currently_active', 'Aktivní') }})
          </span>
        </button>
      </div>
    </div>

    <!-- Main Content - 3 Columns -->
    <div v-if="currentViewCompany && !isLoadingCompany && currentCompanyData" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      
      <!-- Column 1: Company Information -->
      <div>
        <div class="card">
          <div class="card-content p-4">
            <h2 class="text-lg font-semibold mb-4">{{ $t('companies.workspace_information_title', 'Informace') }}</h2>
            
            <!-- Show form only if user can edit -->
            <div v-if="canEditCurrentCompany()">
              <form @submit.prevent="updateCompanyInfo">
                <div class="space-y-3">
                  <div class="form-group">
                    <label for="company_name" class="form-label text-sm">{{ $t('name', 'Název') }}</label>
                    <input 
                      id="company_name" 
                      v-model="currentCompanyData.name" 
                      type="text" 
                      class="form-input" 
                      required
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="company_address" class="form-label text-sm">{{ $t('address', 'Adresa') }}</label>
                    <input 
                      id="company_address" 
                      v-model="currentCompanyData.address" 
                      type="text" 
                      class="form-input"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="company_phone" class="form-label text-sm">{{ $t('phone', 'Telefon') }}</label>
                    <input 
                      id="company_phone" 
                      v-model="currentCompanyData.phone" 
                      type="text" 
                      class="form-input"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="company_web" class="form-label text-sm">{{ $t('web', 'Web') }}</label>
                    <input 
                      id="company_web" 
                      v-model="currentCompanyData.web" 
                      type="text" 
                      class="form-input"
                    >
                  </div>
                  
                  <div class="form-group">
                    <label for="company_description" class="form-label text-sm">{{ $t('description', 'Popis') }}</label>
                    <textarea 
                      id="company_description" 
                      v-model="currentCompanyData.description" 
                      class="form-textarea"
                      rows="3"
                    ></textarea>
                  </div>
                  
                  <div class="flex justify-end">
                    <button 
                      type="submit" 
                      class="btn btn-primary btn-small"
                      :disabled="isSavingInfo"
                    >
                      {{ isSavingInfo ? $t('saving', 'Ukládání...') : $t('companies.save_information_button', 'Uložit') }}
                    </button>
                  </div>
                </div>
              </form>
            </div>
            
            <!-- Show read-only text if user cannot edit -->
            <div v-else class="space-y-3">
              <div>
                <span class="text-sm text-gray-500">{{ $t('name', 'Název') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.name || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('address', 'Adresa') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.address || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('phone', 'Telefon') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.phone || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('web', 'Web') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.web || '-' }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('description', 'Popis') }}:</span>
                <p class="text-gray-800">{{ currentCompanyData.description || '-' }}</p>
              </div>
            </div>
            
            <!-- Company Actions -->
            <hr class="my-4" />
            <div class="flex flex-col gap-2">
              <button 
                v-if="currentViewCompany !== currentTenant"
                @click="switchAssignment(currentViewCompany)" 
                class="btn btn-outline btn-small ml-auto"
              >
                {{ $t('companies.switch_to_this', 'Přepnout sem') }}
              </button>
              <button 
                @click="confirmLeaveCompany(currentViewCompany, currentCompanyData.name)" 
                class="btn btn-outline btn-danger-light btn-small ml-auto"
              >
                {{ $t('disconnect', 'Odpojit se') }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Column 2: Company Settings -->
      <div>
        <div class="card">
          <div class="card-content p-4">
            <h2 class="text-lg font-semibold mb-4">{{ $t('settings', 'Nastavení') }}</h2>
            
            <!-- Show settings form only if user can edit -->
            <div v-if="canEditCurrentCompany() && currentCompanySettings">
              <form @submit.prevent="updateCompanySettings">
                <div class="space-y-3">
                  <div class="form-group">
                    <label for="break_duration" class="form-label text-sm">{{ $t('companies.break_duration_label', 'Délka přestávky (minuty)') }}</label>
                    <input 
                      v-model.number="currentCompanySettings.break_duration" 
                      type="number" 
                      id="break_duration" 
                      class="form-input"
                    >
                  </div>
                  
                  <div class="form-checkbox-group">
                    <input 
                      v-model="currentCompanySettings.approve_vacations" 
                      type="checkbox" 
                      id="approve_vacations" 
                      class="form-checkbox"
                    >
                    <label for="approve_vacations" class="form-checkbox-label text-sm">
                      {{ $t('companies.approve_vacations_label', 'Schvalování dovolené') }}
                    </label>
                  </div>
                  
                  <div class="form-checkbox-group">
                    <input 
                      v-model="currentCompanySettings.daily_team_reports" 
                      type="checkbox" 
                      id="daily_team_reports" 
                      class="form-checkbox"
                      :disabled="!canReceiveStatusEmails()"
                    >
                    <label 
                      for="daily_team_reports" 
                      class="form-checkbox-label text-sm"
                      :class="{ 'text-gray-400 cursor-not-allowed': !canReceiveStatusEmails() }"
                    >
                      {{ $t('companies.daily_team_reports_label', 'Tým v práci na e-mail') }}
                      <span v-if="!canReceiveStatusEmails()" class="text-xs block">
                        ({{ $t('companies.requires_plus_plan', 'Plus plán') }})
                      </span>
                    </label>
                  </div>
                  
                  <div class="flex justify-end">
                    <button 
                      type="submit" 
                      class="btn btn-primary btn-small"
                      :disabled="isSavingSettings"
                    >
                      {{ isSavingSettings ? $t('saving', 'Ukládání...') : $t('companies.update_settings_button', 'Aktualizovat') }}
                    </button>
                  </div>
                </div>
              </form>
            </div>
            
            <!-- Show read-only settings if user cannot edit -->
            <div v-else-if="currentCompanySettings" class="space-y-3">
              <div>
                <span class="text-sm text-gray-500">{{ $t('companies.break_duration_label', 'Délka přestávky (minuty)') }}:</span>
                <p class="text-gray-800">{{ currentCompanySettings.break_duration || 30 }}</p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('companies.approve_vacations_label', 'Schvalování dovolené') }}:</span>
                <p class="text-gray-800">
                  {{ currentCompanySettings.approve_vacations ? $t('yes', 'Ano') : $t('no', 'Ne') }}
                </p>
              </div>
              
              <div>
                <span class="text-sm text-gray-500">{{ $t('companies.daily_team_reports_label', 'Tým v práci na e-mail') }}:</span>
                <p class="text-gray-800">
                  {{ currentCompanySettings.daily_team_reports ? $t('yes', 'Ano') : $t('no', 'Ne') }}
                  <span v-if="!canReceiveStatusEmails()" class="text-xs text-gray-500">
                    ({{ $t('companies.requires_plus_plan', 'Plus plán') }})
                  </span>
                </p>
              </div>
            </div>
            
            <!-- Message if settings not available -->
            <div v-else class="text-gray-500 text-sm">
              {{ $t('companies.settings_not_available', 'Nastavení není k dispozici') }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Column 3: Subscription/Plan Card -->
      <div>
        <div class="card border-2 border-dashed border-gray-300">
          <div class="card-content p-4 space-y-3">
            <div>
              <h2 class="text-lg font-semibold">
                {{ $t('companies.your_plan', 'Váš plán') }}
              </h2>
              <span class="inline-block mt-1 px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                {{ getPlanType() }}
              </span>
            </div>
            
            <p class="text-gray-600 text-sm">
              {{ getUpgradeMessage() }}
            </p>
            
            <ul class="text-sm space-y-1">
              <li v-for="feature in getPlanFeatures()" :key="feature" class="flex items-start">
                <svg class="w-4 h-4 mr-1 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span>{{ feature }}</span>
              </li>
            </ul>
            
            <div class="flex justify-end">
              <button 
                v-if="canUpgrade()"
                class="btn btn-primary btn-small"
                @click="navigateToUpgrade"
              >
                {{ getUpgradeButtonText() }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
    </div>

    <!-- Loading State -->
    <div v-else-if="isLoadingCompany" class="text-center text-gray-500">
      {{ $t('loading', 'Načítání...') }}
    </div>

    <!-- Create Company Modal (kept for new company creation) -->
    <div v-if="showFormModal && formIsNew" class="modal-overlay">
      <div class="modal-container" style="min-height: auto; max-width: 650px;">
        <div class="modal-header">
          <h3 class="text-lg font-semibold">{{ $t('companies.create_new_workspace_title', 'Vytvořit nový pracovní prostor') }}</h3>
          <button @click="closeFormModal" class="close-btn">&times;</button>
        </div>
        <div class="central-modal-content">
          <CompanyForm 
            :company-id="formCompanyId" 
            :is-new="formIsNew"
            :status-emails="can('can_receive_team_status_emails?')"
            @company-created="handleCompanyCreated"  
            @company-updated="handleCompanyUpdated" 
            @settings-updated="handleSettingsUpdated"
            @logo-updated="handleLogoUpdated" 
            @error="handleError"
          />
        </div>
      </div>
    </div>

    <!-- Leave Company Confirmation Modal -->
    <div v-if="showLeaveConfirm" class="modal-overlay">
       <div class="modal-container" style="min-height: auto; max-width: 500px;">
          <div class="modal-header">
            <h3 class="text-lg font-semibold text-red-600">{{ $t('companies.leave_workspace_warning_title', 'POZOR! Opuštění pracovního prostoru') }}</h3>
             <button @click="cancelLeave" class="close-btn">&times;</button>
          </div>
          <div class="central-modal-content">
            <p class="mb-2">{{ $t('companies.leave_workspace_confirm_text_1', 'Opravdu chcete opustit pracovní prostor') }} <strong>{{ companyToLeave.name }}</strong>?</p>
            <p class="text-sm text-gray-600 mb-4">{{ $t('companies.leave_workspace_confirm_text_2', 'Tato akce je nevratná. Ztratíte přístup ke všem datům v tomto pracovním prostoru.') }}</p>
            <p class="mb-2">{{ $t('companies.enter_to_confirm', 'Pro potvrzení zadejte:') }} <strong class="text-red-700">{{ leaveConfirmationCode }}</strong></p>
            <input 
              v-model="confirmationInput" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 mb-4" 
              :placeholder="$t('companies.enter_confirmation_code_placeholder', 'Zadejte potvrzovací kód')">
             <div class="modal-footer justify-between"> 
              <button @click="cancelLeave" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
              <button @click="executeLeave" class="btn btn-danger btn-primary" :disabled="confirmationInput !== leaveConfirmationCode">
                {{ $t('companies.leave_workspace_button', 'Opustit pracovní prostor') }}
              </button>
            </div>
          </div>
       </div>
    </div>

  </div>
</template>

<script>
import authorizationMixin from '../../mixins/authorizationMixin';
import axios from 'axios';
import AuthService from '../../services/authService';
import CompanyForm from './EditCompany.vue';
import CompanyLogo from './CompanyLogo.vue';

export default {
  mixins: [authorizationMixin],
  components: {
    CompanyForm,
    CompanyLogo
  },
  data() {
    return {
      companyUserRoles: [],
      currentTenant: null,
      currentViewCompany: null, // Currently displayed company
      currentCompanyData: null, // Data for displayed company
      currentCompanySettings: null, // Settings for displayed company
      isLoadingCompany: false,
      isLoadingSettings: false,
      isSavingInfo: false,
      isSavingSettings: false,
      showLeaveConfirm: false,
      confirmationInput: '',
      leaveConfirmationCode: '',
      companyToLeave: {
        id: null,
        name: ''
      },
      showFormModal: false,
      formCompanyId: null,
      formIsNew: false,
      showSuccessMessage: false,
      connectedCompanyName: '',
      highlightedCompanyId: null,
      successTimeout: null,
      errors: {}
    };
  },
  created() {
    this.fetchCompanyUserRoles();
    this.handleSuccessFlow();
  },
  methods: {
    fetchCompanyUserRoles() {
      // Use JWT-compatible API endpoint with dual authentication support
      // This endpoint supports JWT-first authentication with session fallback and provides
      // enhanced security by filtering to only active company roles
      axios.get('/api/v1/companies', {
        headers: {
          'Accept': 'application/json'
        }
      })
        .then(response => {
          this.companyUserRoles = response.data.company_user_roles;
          this.currentTenant = response.data.current_tenant;
          
          // Set the current view company to the active tenant by default
          if (!this.currentViewCompany && this.currentTenant) {
            this.currentViewCompany = this.currentTenant;
            this.loadCompanyData(this.currentViewCompany);
            this.loadCompanySettings(this.currentViewCompany);
          }
          
          // After data loads, scroll to highlighted company if needed
          if (this.highlightedCompanyId) {
            this.$nextTick(() => {
              this.scrollToHighlightedCompany();
            });
          }
        })
        .catch(error => {
          console.error('Error fetching company user roles:', error);
          
          // If JWT API fails, we could fallback to the legacy endpoint
          // but this should not be needed since the JWT API supports session fallback
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: this.$t('companies.error_loading_workspaces', 'Nepodařilo se načíst pracovní prostory'), 
              type: 'error' 
            }
          }));
        });
    },
    async switchAssignment(companyId) {
      try {
        // Use AuthService for JWT-first company switching with session fallback
        const result = await AuthService.switchCompany(companyId);
        
        if (result.success) {
          // Update the current tenant in our local state
          this.currentTenant = companyId;
          
          // If using JWT, company data is already updated in the store
          // If using session fallback, reload user data to get updated company context
          if (result.authMethod === 'session') {
            await this.$store.dispatch('userStore/fetchUserData');
          } else if (result.authMethod === 'jwt') {
            // For JWT, we might still want to refresh user data to ensure consistency
            // This is especially important for complex authorization state
            try {
              await this.$store.dispatch('userStore/fetchUserData');
            } catch (error) {
              console.warn('Failed to refresh user data after JWT company switch:', error);
              // Don't fail the company switch for this, the token already contains updated company context
            }
          }
          
          // Show success message
          const successMessage = result.message || this.$t('companies.switched_successfully', 'Pracovní prostor byl přepnut');
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { text: successMessage, type: 'success' }
          }));
          
          // Stay on companies index page instead of redirecting to dashboard
          // This allows user to see the updated company state and switch to other companies if needed
          console.log('[COMPANIES] Company switched successfully, staying on companies index page');
        }
      } catch (error) {
        console.error('Error switching company:', error);
        
        // Extract meaningful error message
        let errorMessage = this.$t('companies.switch_failed', 'Nepodařilo se přepnout pracovní prostor');
        if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { text: errorMessage, type: 'error' }
        }));
      }
    },
    selectCompany(companyId) {
      this.currentViewCompany = companyId;
      this.loadCompanyData(companyId);
      this.loadCompanySettings(companyId);
    },
    async loadCompanyData(companyId) {
      this.isLoadingCompany = true;
      try {
        const response = await axios.get(`/companies/${companyId}/edit.json`);
        this.currentCompanyData = response.data.company;
      } catch (error) {
        console.error('Error loading company data:', error);
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.error_loading_workspace_data', 'Nepodařilo se načíst data pracovního prostoru'), 
            type: 'error' 
          }
        }));
      } finally {
        this.isLoadingCompany = false;
      }
    },
    async loadCompanySettings(companyId) {
      this.isLoadingSettings = true;
      try {
        // For now, we'll load the current tenant's settings
        // In the future, we might need an unscoped endpoint or context switching
        if (companyId === this.currentTenant) {
          const response = await axios.get('/api/v1/company_settings', {
            headers: { 'Accept': 'application/json' }
          });
          this.currentCompanySettings = response.data.company_setting;
        } else {
          // For other companies, we might not have settings access
          // Initialize with default values
          this.currentCompanySettings = {
            break_duration: 30,
            approve_vacations: false,
            daily_team_reports: false
          };
        }
      } catch (error) {
        console.error('Error loading company settings:', error);
        // Initialize with defaults on error
        this.currentCompanySettings = {
          break_duration: 30,
          approve_vacations: false,
          daily_team_reports: false
        };
      } finally {
        this.isLoadingSettings = false;
      }
    },
    async updateCompanyInfo() {
      if (!this.currentViewCompany || !this.currentCompanyData) return;
      
      this.isSavingInfo = true;
      try {
        const response = await axios.put(`/companies/${this.currentViewCompany}.json`, { 
          company: this.currentCompanyData 
        });
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.workspace_updated_success', 'Pracovní prostor byl úspěšně aktualizován'),
            type: 'success' 
          }
        }));
        // Refresh the company list to show updated name
        this.fetchCompanyUserRoles();
      } catch (error) {
        console.error('Error updating company info:', error);
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.error_updating_workspace', 'Nepodařilo se aktualizovat pracovní prostor'),
            type: 'error' 
          }
        }));
      } finally {
        this.isSavingInfo = false;
      }
    },
    async updateCompanySettings() {
      if (!this.currentViewCompany || !this.currentCompanySettings) return;
      
      // Only allow settings update for current tenant
      if (this.currentViewCompany !== this.currentTenant) {
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.cannot_update_other_settings', 'Nemůžete aktualizovat nastavení jiného pracovního prostoru'),
            type: 'error' 
          }
        }));
        return;
      }
      
      this.isSavingSettings = true;
      try {
        const settingsToUpdate = {
          break_duration: this.currentCompanySettings.break_duration,
          approve_vacations: this.currentCompanySettings.approve_vacations,
          daily_team_reports: this.currentCompanySettings.daily_team_reports
        };
        
        const response = await axios.patch('/api/v1/company_settings', { 
          company_setting: settingsToUpdate 
        }, {
          headers: { 'Accept': 'application/json' }
        });
        
        // Refresh the company settings store
        this.$store.dispatch('companySettingsStore/fetchSettings', { force: true });
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.settings_updated_success', 'Nastavení bylo úspěšně aktualizováno'),
            type: 'success' 
          }
        }));
      } catch (error) {
        console.error('Error updating company settings:', error);
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { 
            text: this.$t('companies.error_updating_settings', 'Nepodařilo se aktualizovat nastavení'),
            type: 'error' 
          }
        }));
      } finally {
        this.isSavingSettings = false;
      }
    },
    canEditCurrentCompany() {
      const role = this.companyUserRoles.find(r => r.company.id === this.currentViewCompany);
      if (!role) return false;
      return this.canEditCompany(role);
    },
    canReceiveStatusEmails() {
      // Check if current company has Plus plan for email features
      // For now, use the can() helper from authorization mixin
      return this.can('can_receive_team_status_emails?');
    },
    // Subscription/Plan helper methods
    getPlanName() {
      // TODO: Get actual plan from company data
      return this.$t('companies.plan_free', 'Free');
    },
    getPlanType() {
      // TODO: Get actual plan type
      return this.$t('companies.plan_freemium', 'Freemium');
    },
    getUpgradeMessage() {
      return this.$t('companies.upgrade_message', 'Používáte bezplatnou verzi. Upgradujte na Plus pro odemknutí užitečných funkcí, které vám ušetří čas a poskytnou klid.');
    },
    getPlanFeatures() {
      return [
        this.$t('companies.feature_realtime_overview', 'Přehled kdo je na místě v reálném čase'),
        this.$t('companies.feature_smart_bookings', 'Chytré rezervace s ohledem na rizika'),
        this.$t('companies.feature_team_planner', 'Jednoduché nástroje pro plánování týmu'),
        this.$t('companies.feature_more_coming', '+ další funkce přidávány pravidelně')
      ];
    },
    canUpgrade() {
      // TODO: Check if can upgrade based on current plan
      return true;
    },
    getUpgradeButtonText() {
      return this.$t('companies.upgrade_to_plus', 'Upgradovat na Plus za €9/měsíc');
    },
    navigateToUpgrade() {
      // TODO: Navigate to upgrade page
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { 
          text: this.$t('companies.upgrade_coming_soon', 'Funkce upgradu bude brzy k dispozici'),
          type: 'info' 
        }
      }));
    },
    openCreateModal() {
      this.formCompanyId = null;
      this.formIsNew = true;
      this.showFormModal = true;
    },
    closeFormModal() {
      this.showFormModal = false;
      this.formCompanyId = null;
      this.formIsNew = false;
    },
    handleCompanyCreated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
      this.fetchCompanyUserRoles(); 
      this.closeFormModal();
    },
    handleCompanyUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
      this.fetchCompanyUserRoles(); 
      this.closeFormModal();
    },
    handleSettingsUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
    },
    handleLogoUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
    },
    handleError(errorMessage) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: errorMessage, type: 'error' }
      }));
    },
    confirmLeaveCompany(companyId, companyName) {
      this.companyToLeave = {
        id: companyId,
        name: companyName
      };
      this.leaveConfirmationCode = this.$t('companies.leave_confirmation_code_prefix', 'ODPOJIT-SE-TED-') + companyId;
      this.confirmationInput = '';
      this.showLeaveConfirm = true;
    },
    cancelLeave() {
      this.showLeaveConfirm = false;
      this.confirmationInput = '';
      this.companyToLeave = { id: null, name: '' };
    },
    executeLeave() {
      if (this.confirmationInput !== this.leaveConfirmationCode) return;
      
      axios.post(`/companies/${this.companyToLeave.id}/leave`)
        .then(response => {
          this.showLeaveConfirm = false;
          this.companyUserRoles = this.companyUserRoles.filter(
            role => role.company.id !== this.companyToLeave.id
          );
          
          if (this.companyToLeave.id === this.currentTenant) {
            window.location.href = '/';
          }
        })
        .catch(error => {
          console.error('Error leaving company:', error);
          let message = this.$t('companies.error_leaving_workspace', 'Nepodařilo se opustit pracovní prostor.');
          if (error.response && error.response.data && error.response.data.message) {
            message = error.response.data.message;
          }
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { text: message, type: 'error' }
          }));
        });
    },
    canEditCompany(role) {
      // Only owners can edit companies, or admin/supervisor with advanced roles enabled
      if (role.role.name === 'owner') {
        return true;
      }
      
      // Admin/supervisor can edit only if company has plus/premium plan for advanced roles
      if (role.role.name === 'admin' || role.role.name === 'supervisor') {
        // Check if company has advanced roles enabled (plus/premium plan)
        // This is a simplified check - ideally we'd have this info in the role data
        return false; // For now, be conservative and only allow owners
      }
      
      return false;
    },
    handleSuccessFlow() {
      // Check for success query parameters
      if (this.$route.query.success === 'connected') {
        this.showSuccessMessage = true;
        this.connectedCompanyName = this.$route.query.company || '';
        this.highlightedCompanyId = this.$route.query.highlight || null;
        
        // Auto-dismiss success message after 6 seconds
        this.successTimeout = setTimeout(() => {
          this.showSuccessMessage = false;
        }, 6000);
        
        // Clean up query parameters after a short delay to prevent stale state
        // but allow time for the success message to be displayed
        setTimeout(() => {
          this.$router.replace({
            path: this.$route.path,
            query: {}
          });
        }, 1000);
      }
    },
    isHighlighted(companyId) {
      return this.highlightedCompanyId && this.highlightedCompanyId == companyId;
    },
    scrollToHighlightedCompany() {
      if (!this.highlightedCompanyId) return;
      
      const element = this.$refs[`company-${this.highlightedCompanyId}`];
      if (element && element[0]) {
        element[0].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  },
  beforeDestroy() {
    // Clean up timeout on component destroy
    if (this.successTimeout) {
      clearTimeout(this.successTimeout);
    }
  }
};
</script>

<style scoped>
/* Company selector badges - simple without hover */
.company-selector-badge {
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: white;
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}

.company-selector-active {
  background-color: #dbeafe;
  border-color: #60a5fa;
  color: #1e40af;
  font-weight: 500;
}

.company-selector-current {
  border-color: #10b981;
}

.success-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure success message is visible on mobile */
@media (max-width: 768px) {
  .success-message {
    margin: 0 -1rem 1.5rem -1rem;
  }
}
</style>